import { createClient } from '@supabase/supabase-js';

// These are already the correct Supabase project details
export const SUPABASE_URL = 'https://zosigteeewfjkkfxjsow.supabase.co';
export const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpvc2lndGVlZXdmamtrZnhqc293Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NTY2MjYsImV4cCI6MjA2NDIzMjYyNn0.iactkqIiNC7UsHhuO5G8D3i-1c8gJIaLqzrnVrGr1bA';

// Initialize the Supabase client with proper error handling
export const supabase = createClient(
  SUPABASE_URL, 
  SUPABASE_ANON_KEY, 
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true
    }
  }
);