// Smart Office Assistant - Database Connection Test
// This script tests the Supabase database connection and basic functionality

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://zosigteeewfjkkfxjsow.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.iactkqIiNC7UsHhuO5G8D3i-1c8gJIaLqzrnVrGr1bA';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseConnection() {
  console.log('🔍 Testing Smart Office Assistant Database Connection...\n');

  try {
    // Test 1: Check if we can connect to Supabase
    console.log('1. Testing Supabase connection...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('rooms')
      .select('count')
      .limit(1);
    
    if (healthError) {
      console.error('❌ Connection failed:', healthError.message);
      return;
    }
    console.log('✅ Supabase connection successful');

    // Test 2: Check if rooms table has data
    console.log('\n2. Testing rooms table...');
    const { data: rooms, error: roomsError } = await supabase
      .from('rooms')
      .select('*')
      .limit(5);
    
    if (roomsError) {
      console.error('❌ Rooms query failed:', roomsError.message);
    } else {
      console.log(`✅ Found ${rooms.length} rooms in database`);
      if (rooms.length > 0) {
        console.log('   Sample room:', rooms[0].name, `(${rooms[0].capacity} capacity)`);
      }
    }

    // Test 3: Check parking spots
    console.log('\n3. Testing parking spots table...');
    const { data: parkingSpots, error: parkingError } = await supabase
      .from('parking_spots')
      .select('spot_type, count(*)')
      .group('spot_type');
    
    if (parkingError) {
      console.error('❌ Parking spots query failed:', parkingError.message);
    } else {
      console.log('✅ Parking spots data:');
      parkingSpots.forEach(spot => {
        console.log(`   ${spot.spot_type}: ${spot.count} spots`);
      });
    }

    // Test 4: Check system settings
    console.log('\n4. Testing system settings...');
    const { data: settings, error: settingsError } = await supabase
      .from('system_settings')
      .select('setting_key, setting_value')
      .limit(5);
    
    if (settingsError) {
      console.error('❌ System settings query failed:', settingsError.message);
    } else {
      console.log(`✅ Found ${settings.length} system settings`);
      settings.forEach(setting => {
        console.log(`   ${setting.setting_key}: ${setting.setting_value}`);
      });
    }

    // Test 5: Check sample users
    console.log('\n5. Testing users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('email, role')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users query failed:', usersError.message);
    } else {
      console.log(`✅ Found ${users.length} users in database`);
      users.forEach(user => {
        console.log(`   ${user.email} (${user.role})`);
      });
    }

    // Test 6: Test room availability view
    console.log('\n6. Testing room availability view...');
    const { data: availability, error: availabilityError } = await supabase
      .from('room_availability')
      .select('*')
      .limit(3);
    
    if (availabilityError) {
      console.error('❌ Room availability view failed:', availabilityError.message);
    } else {
      console.log(`✅ Room availability view working (${availability.length} rooms)`);
      availability.forEach(room => {
        console.log(`   ${room.name}: ${room.current_bookings} current bookings`);
      });
    }

    console.log('\n🎉 Database tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('   - Database connection: Working');
    console.log('   - Tables: Created and populated');
    console.log('   - Views: Functional');
    console.log('   - Sample data: Available');

  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

// Run the tests
testDatabaseConnection();
