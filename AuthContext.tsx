import React, { createContext, useState, ReactNode, useEffect } from 'react';
import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Define user employee details interface
export interface EmployeeDetails {
  fullName: string;
  employeeId: string;
  dateOfJoining: string;
  workHours: string;
  workMode: string;
}

// Define user preferences interface
export interface UserPreferences {
  vehicle?: {
    type: 'Car' | 'Bike' | 'None';
    registrationNumber?: string;
  };
  reminders: {
    checkinReminder: boolean;
    checkinReminderTime: number; // minutes before
    occupancyReminder: boolean;
    occupancyThreshold: number; // percentage
  };
}

interface AuthContextValue {
  user: { 
    id: string; 
    role: string; 
    email: string;
    employeeDetails?: EmployeeDetails;
    preferences?: UserPreferences;
    isFirstTimeUser: boolean;
  } | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  updateEmployeeDetails: (details: EmployeeDetails) => Promise<void>;
  updateUserPreferences: (preferences: UserPreferences) => Promise<void>;
  completeOnboarding: () => Promise<void>;
}

// Mock users for testing (since we can't create real users in read-only mode)
const MOCK_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'user123',
    role: 'user',
    employeeDetails: {
      fullName: 'Alex Johnson',
      employeeId: 'EMP-2025-001',
      dateOfJoining: '2025-01-15',
      workHours: '9:00 AM - 5:00 PM',
      workMode: 'hybrid',
    },
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    employeeDetails: {
      fullName: 'Morgan Taylor',
      employeeId: 'EMP-2023-042',
      dateOfJoining: '2023-06-10',
      workHours: '8:30 AM - 4:30 PM',
      workMode: 'in-office',
    },
  }
];

// Create context with default values to prevent "signIn is not a function" errors
const defaultContextValue: AuthContextValue = {
  user: null,
  loading: true,
  signIn: async () => ({ error: new Error('Auth context not initialized') }),
  signOut: async () => { console.warn('Auth context not initialized') },
  updateEmployeeDetails: async () => { console.warn('Auth context not initialized') },
  updateUserPreferences: async () => { console.warn('Auth context not initialized') },
  completeOnboarding: async () => { console.warn('Auth context not initialized') }
};

export const AuthContext = createContext<AuthContextValue>(defaultContextValue);

// Helper for storage - works on both web and native
const storage = {
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem(key, value);
      } else {
        await AsyncStorage.setItem(key, value);
      }
    } catch (e) {
      console.error('Storage setItem error:', e);
    }
  },
  getItem: async (key: string): Promise<string | null> => {
    try {
      if (Platform.OS === 'web') {
        return localStorage.getItem(key);
      } else {
        return await AsyncStorage.getItem(key);
      }
    } catch (e) {
      console.error('Storage getItem error:', e);
      return null;
    }
  },
  removeItem: async (key: string): Promise<void> => {
    try {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (e) {
      console.error('Storage removeItem error:', e);
    }
  }
};

// Default preferences
const DEFAULT_PREFERENCES: UserPreferences = {
  vehicle: {
    type: 'None',
  },
  reminders: {
    checkinReminder: true,
    checkinReminderTime: 15, // 15 minutes before
    occupancyReminder: false,
    occupancyThreshold: 70, // 70%
  },
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthContextValue['user']>(null);
  const [loading, setLoading] = useState<boolean>(true); // Start with loading true to check for session

  // Update employee details
  const updateEmployeeDetails = async (details: EmployeeDetails) => {
    if (!user) return;
    
    try {
      const updatedUser = {
        ...user,
        employeeDetails: details,
      };
      
      setUser(updatedUser);
      
      // Store updated user data
      if (user.id) {
        await storage.setItem('mockUser', JSON.stringify(updatedUser));
      }
    } catch (err) {
      console.error('Error updating employee details:', err);
    }
  };

  // Update user preferences
  const updateUserPreferences = async (preferences: UserPreferences) => {
    if (!user) return;
    
    try {
      const updatedUser = {
        ...user,
        preferences: {
          ...user.preferences,
          ...preferences,
        },
      };
      
      setUser(updatedUser);
      
      // Store updated user data
      if (user.id) {
        await storage.setItem('mockUser', JSON.stringify(updatedUser));
      }
    } catch (err) {
      console.error('Error updating user preferences:', err);
    }
  };

  // Mark onboarding as complete
  const completeOnboarding = async () => {
    if (!user) return;
    
    try {
      console.log('Completing onboarding for user:', user.id);
      const updatedUser = {
        ...user,
        isFirstTimeUser: false,
      };
      
      setUser(updatedUser);
      
      // Store updated user data
      if (user.id) {
        await storage.setItem('mockUser', JSON.stringify(updatedUser));
        await storage.setItem(`onboarded-${user.id}`, 'true');
      }
    } catch (err) {
      console.error('Error completing onboarding:', err);
    }
  };

  // signIn checks credentials against mock users first, then falls back to Supabase
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      console.log('Attempting to sign in with:', email);
      
      if (!email || !password) {
        return { error: new Error('Email and password are required') };
      }
      
      // First check if this is one of our mock users
      const mockUser = MOCK_USERS.find(u => u.email === email && u.password === password);
      
      if (mockUser) {
        // If this is a mock user, simulate a successful sign in
        const isOnboarded = await storage.getItem(`onboarded-${mockUser.id}`);
        
        const userData = {
          id: mockUser.id,
          email: mockUser.email,
          role: mockUser.role,
          employeeDetails: mockUser.employeeDetails,
          preferences: DEFAULT_PREFERENCES,
          isFirstTimeUser: isOnboarded !== 'true',
        };
        
        setUser(userData);
        
        // Store the mock user in storage
        await storage.setItem('mockUser', JSON.stringify(userData));
        
        console.log('Mock user signed in:', mockUser.email, 'First time:', userData.isFirstTimeUser);
        return { error: null };
      }
      
      // If not a mock user, try Supabase auth
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) {
        console.error('Sign in error:', error.message);
        return { error };
      }
      
      if (!data.user) {
        return { error: new Error('No user returned from authentication') };
      }
      
      const supaUser = data.user;
      // Extract role from user metadata or default to 'user'
      const role = (supaUser.user_metadata as any)?.role || 'user';
      
      // Check if this user has completed onboarding
      const isOnboarded = await storage.getItem(`onboarded-${supaUser.id}`);
      
      setUser({ 
        id: supaUser.id, 
        role, 
        email: supaUser.email || '',
        isFirstTimeUser: isOnboarded !== 'true',
      });
      
      return { error: null };
    } catch (err) {
      console.error('Unexpected error during sign in:', err);
      return { error: err instanceof Error ? err : new Error('An unexpected error occurred') };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      console.log('Signing out user');
      // Remove mock user from storage
      await storage.removeItem('mockUser');
      // Clear user state
      setUser(null);
      // Also sign out from Supabase to be safe
      await supabase.auth.signOut();
    } catch (err) {
      console.error('Unexpected error during sign out:', err);
    } finally {
      setLoading(false);
    }
  };

  // Sync auth state on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Check if we have a stored mock user
        const storedMockUser = await storage.getItem('mockUser');
        if (storedMockUser) {
          try {
            const mockUser = JSON.parse(storedMockUser);
            // Ensure isFirstTimeUser is correctly set based on onboarding status
            if (mockUser.id) {
              const isOnboarded = await storage.getItem(`onboarded-${mockUser.id}`);
              mockUser.isFirstTimeUser = isOnboarded !== 'true';
            }
            setUser(mockUser);
            setLoading(false);
            return;
          } catch (e) {
            console.error('Error parsing stored mock user:', e);
            await storage.removeItem('mockUser');
          }
        }
        
        // Otherwise check Supabase session
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Session retrieval error:', error.message);
          setLoading(false);
          return;
        }
        
        const session = data.session;
        if (session?.user) {
          const supaUser = session.user;
          const role = (supaUser.user_metadata as any)?.role || 'user';
          
          // Check if this user has completed onboarding
          const isOnboarded = await storage.getItem(`onboarded-${supaUser.id}`);
          
          setUser({ 
            id: supaUser.id, 
            role, 
            email: supaUser.email || '',
            isFirstTimeUser: isOnboarded !== 'true',
          });
        }
      } catch (err) {
        console.error('Unexpected error checking session:', err);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    const { data: listener } = supabase.auth.onAuthStateChange(async (_event, session) => {
      if (session?.user) {
        const supaUser = session.user;
        const role = (supaUser.user_metadata as any)?.role || 'user';
        
        // Check if this user has completed onboarding
        const isOnboarded = await storage.getItem(`onboarded-${supaUser.id}`);
        
        setUser({ 
          id: supaUser.id, 
          role, 
          email: supaUser.email || '',
          isFirstTimeUser: isOnboarded !== 'true',
        });
      } else {
        // Don't clear mock user on Supabase auth changes
        const storedMockUser = await storage.getItem('mockUser');
        if (!storedMockUser) {
          setUser(null);
        }
      }
    });
    
    return () => {
      listener.subscription.unsubscribe();
    };
  }, []);

  return (
    <AuthContext.Provider value={{ 
      user, 
      loading, 
      signIn, 
      signOut, 
      updateEmployeeDetails, 
      updateUserPreferences, 
      completeOnboarding 
    }}>
      {children}
    </AuthContext.Provider>
  );
};