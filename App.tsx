import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useContext, useEffect } from 'react';
import { StyleSheet, View, ActivityIndicator, Text } from 'react-native';
import { SafeAreaProvider } from "react-native-safe-area-context"
import { Toaster } from 'sonner-native';
import HomeScreen from './screens/HomeScreen';
import BookRoomScreen from './screens/BookRoomScreen';
import ParkingScreen from './screens/ParkingScreen';
import AttendanceScreen from './screens/AttendanceScreen';
import AdminDashboardScreen from './screens/AdminDashboardScreen';
import ChatbotScreen from './screens/ChatbotScreen';
import ProfileScreen from './screens/ProfileScreen';
import OnboardingScreen from './screens/OnboardingScreen';
import { AuthProvider, AuthContext } from './AuthContext';
import SignInScreen from './screens/SignInScreen';

// Define parameter types for the root stack
export type RootStackParamList = {
  Home: undefined;
  BookRoom: undefined;
  Parking: undefined;
  Attendance: undefined;
  AdminDashboard: undefined;
  Chatbot: undefined;
  Profile: undefined;
  Onboarding: undefined;
  SignIn: undefined;
};

// Auth stack for unauthenticated users
const AuthStack = createNativeStackNavigator<{ SignIn: undefined }>();
function AuthStackNavigator() {
  return (
    <AuthStack.Navigator screenOptions={{ headerShown: false }}>
      <AuthStack.Screen name="SignIn" component={SignInScreen} />
    </AuthStack.Navigator>
  );
}

// App stack for authenticated users
const AppStack = createNativeStackNavigator<RootStackParamList>();
function AppStackNavigator() {
  const { user } = useContext(AuthContext);
  
  // Added safety check
  if (!user) {
    return <AuthStackNavigator />;
  }
  
  return (
    <AppStack.Navigator
      screenOptions={{ headerShown: false, animation: 'slide_from_right', contentStyle: { backgroundColor: '#FFFFFF' } }}
    >
      {user?.isFirstTimeUser ? (
        <AppStack.Screen 
          name="Onboarding" 
          component={OnboardingScreen}
          options={{ gestureEnabled: false }} // Prevent back gestures during onboarding
        />
      ) : (
        <>
          <AppStack.Screen name="Home" component={HomeScreen} />
          <AppStack.Screen name="BookRoom" component={BookRoomScreen} />
          <AppStack.Screen name="Parking" component={ParkingScreen} />
          <AppStack.Screen name="Attendance" component={AttendanceScreen} />
          <AppStack.Screen name="AdminDashboard" component={AdminDashboardScreen} />
          <AppStack.Screen name="Chatbot" component={ChatbotScreen} />
          <AppStack.Screen name="Profile" component={ProfileScreen} />
          <AppStack.Screen name="Onboarding" component={OnboardingScreen} />
        </>
      )}
    </AppStack.Navigator>
  );
}

function AppContent() {
  const { user, loading } = useContext(AuthContext);
  
  // Add some debugging
  useEffect(() => {
    console.log('Auth state changed:', { user: user ? 'Logged in' : 'Not logged in', loading });
  }, [user, loading]);
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4A80F0" />
        <Text style={styles.loadingText}>Loading your workspace...</Text>
      </View>
    );
  }
  
  return (
    <NavigationContainer>
      {!user ? <AuthStackNavigator /> : <AppStackNavigator />}
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <SafeAreaProvider style={styles.container}>
      <AuthProvider>
        <Toaster richColors closeButton />
        <AppContent />
      </AuthProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    userSelect: "none"
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    textAlign: 'center'
  }
});