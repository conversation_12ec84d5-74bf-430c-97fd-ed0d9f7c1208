{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@supabase/supabase-js": "^2.49.8", "expo": "^52.0.42", "expo-status-bar": "^2.2.3", "react": "18.2.0", "react-dom": "^18.3.1", "react-native": "0.72.6", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-web": "~0.19.6", "sonner-native": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}